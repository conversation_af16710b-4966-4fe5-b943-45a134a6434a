package studentgradecalculator;

import java.util.List;
import java.util.ArrayList;

/**
 * فئة لحساب درجات الطلاب وتصنيفهم
 * Student Grade Calculator Class
 * 
 * <AUTHOR> الطالب]
 * مشروع اختبار البرمجيات والجودة - المشروع الثاني
 * Software Testing and Quality Assurance - Project 2
 */
public class StudentGradeCalculator {

    /**
     * دالة لحساب وتصنيف درجات الطلاب
     * Method to calculate and classify student grades
     * 
     * تحتوي هذه الدالة على أكثر من 6 فروع وحلقتين كما هو مطلوب
     * This method contains more than 6 branches and two loops as required
     * 
     * @param grades قائمة بدرجات الطلاب (List of student grades)
     * @param passingGrade الدرجة المطلوبة للنجاح (Minimum passing grade)
     * @return نتيجة التحليل (Analysis result)
     */
    public GradeAnalysisResult analyzeStudentGrades(List<Double> grades, double passingGrade) {
        
        // فرع 1: التحقق من صحة المدخلات - القائمة الفارغة
        // Branch 1: Input validation - Empty list
        if (grades == null || grades.isEmpty()) {
            return new GradeAnalysisResult("خطأ: درجة النجاح غير صحيحة (يجب أن تكون بين 0-100)", 0, 0, 0, 0, 0);
        }
        
        // فرع 2: التحقق من صحة درجة النجاح
        // Branch 2: Validate passing grade
        if (passingGrade < 0 || passingGrade > 100) {
            return new GradeAnalysisResult("خطأ: درجة النجاح غير صحيحة (يجب أن تكون بين 0-100)", 0, 0, 0, 0, 0);
        }

        // متغيرات لحساب الإحصائيات
        // Variables for calculating statistics
        int totalStudents = grades.size();
        int passedStudents = 0;
        int failedStudents = 0;
        int excellentStudents = 0; // 90+ درجة ممتاز
        int goodStudents = 0;      // 80-89 درجة جيد
        int acceptableStudents = 0; // درجة النجاح إلى 79
        double totalGrades = 0;
        int validGrades = 0; // عدد الدرجات الصحيحة
        
        // الحلقة الأولى: حساب الإحصائيات الأساسية
        // First loop: Calculate basic statistics
        for (Double grade : grades) {
            
            // فرع 3: التحقق من صحة الدرجة الفردية
            // Branch 3: Validate individual grade
            if (grade == null || grade < 0 || grade > 100) {
                continue; // تجاهل الدرجات غير الصحيحة (Skip invalid grades)
            }
            
            validGrades++;
            totalGrades += grade;
            
            // فرع 4: تحديد النجاح أو الرسوب
            // Branch 4: Pass/Fail determination
            if (grade >= passingGrade) {
                passedStudents++;
                
                // فرع 5: تصنيف الطلاب الممتازين (90+)
                // Branch 5: Classification of excellent students (90+)
                if (grade >= 90) {
                    excellentStudents++;
                } 
                // فرع 6: تصنيف الطلاب الجيدين (80-89)
                // Branch 6: Classification of good students (80-89)
                else if (grade >= 80) {
                    goodStudents++;
                } 
                // فرع 7: تصنيف الطلاب المقبولين (درجة النجاح إلى 79)
                // Branch 7: Classification of acceptable students
                else {
                    acceptableStudents++;
                }
            } 
            // فرع 8: الطلاب الراسبون
            // Branch 8: Failed students
            else {
                failedStudents++;
            }
        }
        
        // حساب المتوسط
        // Calculate average
        double averageGrade = validGrades > 0 ? totalGrades / validGrades : 0;
        
        // الحلقة الثانية: تحديد التصنيف العام للفصل
        // Second loop: Determine class overall performance
        String classPerformance = "متوسط"; // القيمة الافتراضية
        
        // حلقة للتحقق من مستويات الأداء المختلفة
        // Loop to check different performance levels
        for (int performanceLevel = 1; performanceLevel <= 4; performanceLevel++) {
            
            // فرع 9: تحديد الأداء الممتاز للفصل
            // Branch 9: Determine excellent class performance
            if (performanceLevel == 1 && averageGrade >= 85) {
                classPerformance = "ممتاز";
                break;
            } 
            // فرع 10: تحديد الأداء الجيد للفصل
            // Branch 10: Determine good class performance
            else if (performanceLevel == 2 && averageGrade >= 75 && averageGrade < 85) {
                classPerformance = "جيد";
                break;
            } 
            // فرع 11: تحديد الأداء الضعيف للفصل
            // Branch 11: Determine poor class performance
            else if (performanceLevel == 3 && averageGrade < 60) {
                classPerformance = "ضعيف";
                break;
            }
            // فرع 12: الأداء المتوسط (القيمة الافتراضية)
            // Branch 12: Average performance (default)
            else if (performanceLevel == 4) {
                classPerformance = "متوسط";
                break;
            }
        }
        
        // إرجاع النتيجة النهائية
        // Return final result
        return new GradeAnalysisResult(
            classPerformance,
            totalStudents,
            passedStudents,
            failedStudents,
            averageGrade,
            excellentStudents,
            goodStudents,
            acceptableStudents
        );
    }
    
    /**
     * فئة داخلية لتخزين نتائج تحليل الدرجات
     * Inner class to store grade analysis results
     */
    public static class GradeAnalysisResult {
        private String classPerformance;
        private int totalStudents;
        private int passedStudents;
        private int failedStudents;
        private double averageGrade;
        private int excellentStudents;
        private int goodStudents;
        private int acceptableStudents;
        
        public GradeAnalysisResult(String classPerformance, int totalStudents, 
                                 int passedStudents, int failedStudents, 
                                 double averageGrade, int excellentStudents,
                                 int goodStudents, int acceptableStudents) {
            this.classPerformance = classPerformance;
            this.totalStudents = totalStudents;
            this.passedStudents = passedStudents;
            this.failedStudents = failedStudents;
            this.averageGrade = averageGrade;
            this.excellentStudents = excellentStudents;
            this.goodStudents = goodStudents;
            this.acceptableStudents = acceptableStudents;
        }
        
        // Getters
        public String getClassPerformance() { return classPerformance; }
        public int getTotalStudents() { return totalStudents; }
        public int getPassedStudents() { return passedStudents; }
        public int getFailedStudents() { return failedStudents; }
        public double getAverageGrade() { return averageGrade; }
        public int getExcellentStudents() { return excellentStudents; }
        public int getGoodStudents() { return goodStudents; }
        public int getAcceptableStudents() { return acceptableStudents; }
        
        @Override
        public String toString() {
            return String.format(
                "=== تحليل درجات الطلاب ===\n" +
                "الأداء العام للفصل: %s\n" +
                "إجمالي عدد الطلاب: %d\n" +
                "الطلاب الناجحون: %d\n" +
                "الطلاب الراسبون: %d\n" +
                "المتوسط العام: %.2f\n" +
                "الطلاب المتفوقون (90+): %d\n" +
                "الطلاب الجيدون (80-89): %d\n" +
                "الطلاب المقبولون: %d\n" +
                "========================",
                classPerformance, totalStudents, passedStudents, 
                failedStudents, averageGrade, excellentStudents,
                goodStudents, acceptableStudents
            );
        }
    }
    
    /**
     * دالة main للاختبار السريع
     * Main method for quick testing
     */
    public static void main(String[] args) {
        System.out.println("=== مشروع اختبار البرمجيات والجودة - المشروع الثاني ===");
        System.out.println("Software Testing and Quality Assurance - Project 2");
        System.out.println();
        
        StudentGradeCalculator calculator = new StudentGradeCalculator();
        
        // مثال سريع
        List<Double> sampleGrades = new ArrayList<>();
        sampleGrades.add(95.0);
        sampleGrades.add(85.0);
        sampleGrades.add(75.0);
        sampleGrades.add(65.0);
        sampleGrades.add(45.0);
        
        GradeAnalysisResult result = calculator.analyzeStudentGrades(sampleGrades, 60.0);
        System.out.println(result.toString());
        
        System.out.println("\n=== ملخص الكود ===");
        System.out.println("عدد الفروع في الكود: 12+ فرع");
        System.out.println("عدد الحلقات في الكود: 2 حلقة");
        System.out.println("الكود جاهز لاختبارات JUnit و Code Coverage و Mutation Testing");
    }
}
