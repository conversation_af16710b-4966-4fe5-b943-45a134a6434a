# دليل أخذ لقطات الشاشة للتقرير
# Screenshot Guide for Report

## 📸 **لقطات الشاشة المطلوبة للتقرير**

### 1. **لقطة شاشة: تشغيل اختبارات JUnit**

**الخطوات:**
1. افتح NetBeans
2. File → Open Project → اختر مجلد `StudentGradeCalculator`
3. انقر بالزر الأيمن على المشروع
4. اختر **"Test"**
5. انتظر حتى تكتمل الاختبارات
6. خذ لقطة شاشة من نافذة **"Test Results"**

**ما يجب أن يظهر في اللقطة:**
- ✅ جميع الاختبارات ناجحة (8/8)
- ⏱️ وقت التنفيذ
- 📊 تفاصيل كل اختبار

---

### 2. **لقطة شاشة: Code Coverage (JaCoCo)**

**الخطوات:**
1. انقر بالزر الأيمن على المشروع
2. اختر **"Test with Code Coverage"**
3. انتظر حتى تكتمل الاختبارات
4. اذهب إلى **Window → IDE Tools → Code Coverage**
5. خذ لقطة شاشة من نافذة **"Code Coverage"**

**ما يجب أن يظهر في اللقطة:**
- 📊 نسبة تغطية الخطوط (Line Coverage)
- 🌿 نسبة تغطية الفروع (Branch Coverage)
- 🎯 نسبة تغطية الطرق (Method Coverage)
- 🟢 الخطوط الخضراء (تم تنفيذها)

---

### 3. **لقطة شاشة: Mutation Testing (PIT)**

#### الطريقة الأولى: إذا كان PIT Plugin مثبت
**الخطوات:**
1. انقر بالزر الأيمن على المشروع
2. اختر **"Run Mutation Tests"**
3. انتظر حتى يكتمل التحليل
4. خذ لقطة شاشة من تقرير PIT

#### الطريقة الثانية: استخدام Maven
**الخطوات:**
1. افتح Terminal في NetBeans (Window → IDE Tools → Terminal)
2. اكتب: `mvn org.pitest:pitest-maven:mutationCoverage`
3. انتظر حتى يكتمل
4. افتح ملف `target/pit-reports/[timestamp]/index.html`
5. خذ لقطة شاشة من التقرير

**ما يجب أن يظهر في اللقطة:**
- 🎯 نسبة قتل المتحورات (Mutation Score)
- 📈 عدد المتحورات المقتولة
- 📉 عدد المتحورات الناجية
- 📊 تفاصيل أنواع التحورات

---

### 4. **لقطة شاشة: تشغيل الكود الرئيسي**

**الخطوات:**
1. انقر بالزر الأيمن على ملف `StudentGradeCalculator.java`
2. اختر **"Run File"**
3. راجع النتائج في نافذة **"Output"**
4. خذ لقطة شاشة من النتائج

**ما يجب أن يظهر في اللقطة:**
- 📋 نتائج تحليل الدرجات
- 📊 الإحصائيات المحسوبة
- ✅ رسالة نجاح التشغيل

---

### 5. **لقطة شاشة: هيكل المشروع**

**الخطوات:**
1. في NetBeans، اذهب إلى نافذة **"Projects"**
2. وسع مجلد المشروع لإظهار جميع الملفات
3. خذ لقطة شاشة تظهر:
   - مجلد `src` مع الكود الرئيسي
   - مجلد `test` مع الاختبارات
   - مجلد `nbproject` مع إعدادات NetBeans

---

### 6. **لقطة شاشة: الكود الرئيسي**

**الخطوات:**
1. افتح ملف `StudentGradeCalculator.java`
2. اذهب إلى الدالة `analyzeStudentGrades`
3. خذ لقطة شاشة تظهر:
   - توقيع الدالة
   - بعض الفروع والحلقات
   - التعليقات العربية والإنجليزية

---

### 7. **لقطة شاشة: ملف الاختبارات**

**الخطوات:**
1. افتح ملف `StudentGradeCalculatorTest.java`
2. خذ لقطة شاشة تظهر:
   - قائمة الاختبارات
   - مثال على اختبار واحد
   - استخدام JUnit annotations

---

## 🎯 **نصائح لأخذ لقطات شاشة جيدة:**

### 1. **الوضوح:**
- تأكد من أن النص واضح ومقروء
- استخدم دقة عالية
- تجنب الضوء الزائد على الشاشة

### 2. **المحتوى:**
- اظهر النتائج المهمة فقط
- تأكد من ظهور النسب والأرقام
- اقطع الأجزاء غير المهمة

### 3. **التنظيم:**
- رتب اللقطات حسب ترتيب التقرير
- اكتب عنوان لكل لقطة
- احفظ اللقطات بأسماء واضحة

### 4. **التسمية المقترحة للملفات:**
```
01_junit_tests_results.png
02_code_coverage_jacoco.png
03_mutation_testing_pit.png
04_main_code_execution.png
05_project_structure.png
06_main_code_screenshot.png
07_test_code_screenshot.png
```

---

## 📝 **كيفية إدراج اللقطات في التقرير:**

### في Word:
1. Insert → Pictures → This Device
2. اختر الصورة
3. اضبط الحجم والموضع
4. أضف عنوان للصورة

### في PDF:
1. استخدم أداة تحرير PDF
2. أو اكتب التقرير في Word ثم احفظه كـ PDF

### في Markdown:
```markdown
![عنوان الصورة](مسار_الصورة.png)
```

---

## ⚠️ **ملاحظات مهمة:**

1. **تأكد من نجاح جميع الاختبارات** قبل أخذ اللقطة
2. **اخفِ المعلومات الشخصية** إذا لزم الأمر
3. **احفظ اللقطات بجودة عالية** للوضوح
4. **اكتب تعليق تحت كل لقطة** في التقرير
5. **تأكد من أن التواريخ والأوقات** تظهر في اللقطات

---

## 🎉 **بعد أخذ جميع اللقطات:**

1. راجع كل لقطة للتأكد من وضوحها
2. رتبها حسب ترتيب التقرير
3. اكتب التقرير النهائي
4. أدرج اللقطات في الأماكن المناسبة
5. راجع التقرير كاملاً قبل التسليم

**الهدف: إثبات أنك طبقت جميع المتطلبات بنجاح! 🏆**
