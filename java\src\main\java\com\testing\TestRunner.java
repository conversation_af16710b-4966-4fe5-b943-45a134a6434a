package com.testing;

import java.util.*;

/**
 * فئة لتشغيل واختبار الكود بشكل بسيط
 * Simple test runner class
 */
public class TestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("مشروع اختبار البرمجيات والجودة - المشروع الثاني");
        System.out.println("Software Testing and Quality Assurance - Project 2");
        System.out.println("========================================");
        System.out.println();
        
        StudentGradeCalculator calculator = new StudentGradeCalculator();
        
        // اختبار 1: حالة الأداء الممتاز
        System.out.println("=== اختبار 1: الأداء الممتاز ===");
        List<Double> excellentGrades = Arrays.asList(95.0, 92.0, 88.0, 90.0, 85.0);
        testGrades(calculator, excellentGrades, 60.0, "الأداء الممتاز");
        
        System.out.println();
        
        // اختبار 2: حالة الأداء المختلط
        System.out.println("=== اختبار 2: الأداء المختلط ===");
        List<Double> mixedGrades = Arrays.asList(85.0, 45.0, 78.0, 55.0, 92.0, 30.0, 88.0);
        testGrades(calculator, mixedGrades, 60.0, "الأداء المختلط");
        
        System.out.println();
        
        // اختبار 3: حالة الأداء الضعيف
        System.out.println("=== اختبار 3: الأداء الضعيف ===");
        List<Double> poorGrades = Arrays.asList(45.0, 35.0, 50.0, 40.0, 55.0);
        testGrades(calculator, poorGrades, 60.0, "الأداء الضعيف");
        
        System.out.println();
        
        // اختبار 4: حالة القائمة الفارغة
        System.out.println("=== اختبار 4: القائمة الفارغة ===");
        List<Double> emptyGrades = new ArrayList<>();
        testGrades(calculator, emptyGrades, 60.0, "القائمة الفارغة");
        
        System.out.println();
        
        // اختبار 5: درجة نجاح غير صحيحة
        System.out.println("=== اختبار 5: درجة النجاح غير الصحيحة ===");
        List<Double> normalGrades = Arrays.asList(85.0, 75.0, 65.0);
        testGrades(calculator, normalGrades, -10.0, "درجة النجاح غير الصحيحة");
        
        System.out.println();
        
        // اختبار 6: درجات مع قيم null
        System.out.println("=== اختبار 6: درجات مع قيم null ===");
        List<Double> gradesWithNull = Arrays.asList(85.0, null, 75.0, 65.0, null);
        testGrades(calculator, gradesWithNull, 60.0, "درجات مع قيم null");
        
        System.out.println();
        System.out.println("========================================");
        System.out.println("تم الانتهاء من جميع الاختبارات!");
        System.out.println("All tests completed!");
        System.out.println("========================================");
        
        // عرض ملخص الفروع والحلقات
        System.out.println();
        System.out.println("=== ملخص الكود ===");
        System.out.println("الفروع المستخدمة في الكود:");
        System.out.println("1. التحقق من القائمة الفارغة");
        System.out.println("2. التحقق من صحة درجة النجاح");
        System.out.println("3. تحديد النجاح/الرسوب");
        System.out.println("4. تصنيف الطلاب الممتازين (90+)");
        System.out.println("5. تصنيف الطلاب الجيدين (80-89)");
        System.out.println("6. تصنيف الطلاب المقبولين");
        System.out.println("7. تصنيف الطلاب الراسبين");
        System.out.println("8. تحديد الأداء الممتاز للفصل");
        System.out.println("9. تحديد الأداء الجيد للفصل");
        System.out.println("10. تحديد الأداء الضعيف للفصل");
        System.out.println();
        System.out.println("الحلقات المستخدمة:");
        System.out.println("1. حلقة for-each لحساب الإحصائيات الأساسية");
        System.out.println("2. حلقة for لتحديد مستوى الأداء العام");
        System.out.println();
        System.out.println("إجمالي الفروع: 10+ فروع");
        System.out.println("إجمالي الحلقات: 2 حلقات");
    }
    
    private static void testGrades(StudentGradeCalculator calculator, 
                                 List<Double> grades, 
                                 double passingGrade, 
                                 String testName) {
        try {
            StudentGradeCalculator.GradeAnalysisResult result = 
                calculator.analyzeStudentGrades(grades, passingGrade);
            
            System.out.println("اسم الاختبار: " + testName);
            System.out.println("الدرجات: " + grades);
            System.out.println("درجة النجاح: " + passingGrade);
            System.out.println("النتيجة:");
            System.out.println(result.toString());
            System.out.println("✓ الاختبار نجح!");
            
        } catch (Exception e) {
            System.out.println("✗ خطأ في الاختبار: " + e.getMessage());
        }
    }
}
