# تقرير مشروع اختبار البرمجيات والجودة - المشروع الثاني
## Software Testing and Quality Assurance - Project 2 Report

**الطالب:** [اسم الطالب]  
**الرقم الجامعي:** [الرقم الجامعي]  
**التاريخ:** 27 سبتمبر 2025  
**الأستاذ:** د. علاء الحليس  

---

## 1. وصف المشروع

تم إنشاء مشروع Java لحساب وتحليل درجات الطلاب يحتوي على:
- دالة معقدة تحتوي على أكثر من 6 فروع وحلقتين
- اختبارات JUnit شاملة (6 حالات اختبار)
- تطبيق Code Coverage باستخدام JaCoCo
- تطبيق Mutation Testing باستخدام PIT

## 2. الكود المطلوب - الدالة الرئيسية

### الدالة: `analyzeStudentGrades`

```java
public GradeAnalysisResult analyzeStudentGrades(List<Double> grades, double passingGrade)
```

### الفروع المستخدمة (10+ فروع):

1. **فرع التحقق من القائمة الفارغة**
   ```java
   if (grades == null || grades.isEmpty())
   ```

2. **فرع التحقق من صحة درجة النجاح**
   ```java
   if (passingGrade < 0 || passingGrade > 100)
   ```

3. **فرع التحقق من صحة الدرجة**
   ```java
   if (grade == null || grade < 0 || grade > 100)
   ```

4. **فرع تحديد النجاح/الرسوب**
   ```java
   if (grade >= passingGrade)
   ```

5. **فرع تصنيف الطلاب الممتازين**
   ```java
   if (grade >= 90)
   ```

6. **فرع تصنيف الطلاب الجيدين**
   ```java
   else if (grade >= 80)
   ```

7. **فرع الطلاب المقبولين** (ضمني - else)

8. **فرع الطلاب الراسبين** (else للفرع الرئيسي)

9. **فرع الأداء الممتاز للفصل**
   ```java
   if (i == 0 && averageGrade >= 85)
   ```

10. **فرع الأداء الجيد للفصل**
    ```java
    else if (i == 1 && averageGrade >= 75)
    ```

11. **فرع الأداء الضعيف للفصل**
    ```java
    else if (i == 2 && averageGrade < 60)
    ```

### الحلقات المستخدمة (2 حلقات):

1. **الحلقة الأولى - حساب الإحصائيات الأساسية:**
   ```java
   for (Double grade : grades) {
       // حساب المجموع والتصنيف
   }
   ```

2. **الحلقة الثانية - تحديد الأداء العام:**
   ```java
   for (int i = 0; i < 3; i++) {
       // تحديد مستوى الأداء
   }
   ```

## 3. اختبارات JUnit

تم إنشاء 6 حالات اختبار شاملة:

### 3.1 اختبار الأداء الممتاز
```java
@Test
void testExcellentPerformance()
```
- **البيانات:** [95.0, 92.0, 88.0, 90.0, 85.0]
- **النتيجة المتوقعة:** أداء ممتاز، 5 طلاب ناجحين، 3 متفوقين

### 3.2 اختبار الأداء المختلط
```java
@Test
void testMixedPerformance()
```
- **البيانات:** [85.0, 45.0, 78.0, 55.0, 92.0, 30.0, 88.0]
- **النتيجة المتوقعة:** أداء جيد، 4 ناجحين، 3 راسبين

### 3.3 اختبار الأداء الضعيف
```java
@Test
void testPoorPerformance()
```
- **البيانات:** [45.0, 35.0, 50.0, 40.0, 55.0]
- **النتيجة المتوقعة:** أداء ضعيف، جميع الطلاب راسبين

### 3.4 اختبار القائمة الفارغة
```java
@Test
void testEmptyGradesList()
```
- **البيانات:** قائمة فارغة
- **النتيجة المتوقعة:** رسالة خطأ

### 3.5 اختبار درجة النجاح غير الصحيحة
```java
@Test
void testInvalidPassingGrade()
```
- **البيانات:** درجة نجاح = -10
- **النتيجة المتوقعة:** رسالة خطأ

### 3.6 اختبار الدرجات مع قيم null
```java
@Test
void testGradesWithNullValues()
```
- **البيانات:** [85.0, null, 75.0, 65.0, null]
- **النتيجة المتوقعة:** تجاهل القيم null والحساب للباقي

## 4. نتائج تشغيل الاختبارات

جميع الاختبارات نجحت بنسبة 100%:

```
✓ اختبار الأداء الممتاز نجح
✓ اختبار الأداء المختلط نجح  
✓ اختبار الأداء الضعيف نجح
✓ اختبار القائمة الفارغة نجح
✓ اختبار درجة النجاح غير الصحيحة نجح
✓ اختبار الدرجات مع قيم null نجح
```

## 5. Code Coverage (تغطية الكود)

### الأدوات المستخدمة:
- **JaCoCo Maven Plugin** لقياس تغطية الكود

### الأوامر للتشغيل:
```bash
mvn test                    # تشغيل الاختبارات
mvn jacoco:report          # إنشاء تقرير التغطية
```

### النتائج المتوقعة:
- **تغطية الخطوط (Line Coverage):** 100%
- **تغطية الفروع (Branch Coverage):** 100%
- **تغطية الطرق (Method Coverage):** 100%

## 6. Mutation Testing (اختبار التحور)

### الأدوات المستخدمة:
- **PIT (Pitest) Maven Plugin** لاختبار التحور

### الأوامر للتشغيل:
```bash
mvn org.pitest:pitest-maven:mutationCoverage
```

### أنواع التحورات المتوقعة:
1. **تغيير العمليات الحسابية** (+, -, *, /)
2. **تغيير العمليات المنطقية** (&&, ||, !)
3. **تغيير عمليات المقارنة** (<, >, <=, >=, ==, !=)
4. **تغيير القيم الثابتة** (الأرقام والنصوص)

### النتائج المتوقعة:
- **نسبة قتل المتحورات:** 85%+ (هدف جيد)
- **المتحورات الناجية:** أقل من 15%

## 7. هيكل المشروع

```
java/
├── pom.xml                                    # إعدادات Maven
├── src/
│   ├── main/java/com/testing/
│   │   ├── StudentGradeCalculator.java       # الكلاس الرئيسي
│   │   └── TestRunner.java                   # مشغل الاختبارات البسيط
│   └── test/java/com/testing/
│       └── StudentGradeCalculatorTest.java   # اختبارات JUnit
├── compile-and-run.bat                       # ملف تشغيل بسيط
└── README.md                                 # دليل المشروع
```

## 8. كيفية تشغيل المشروع

### الطريقة الأولى: باستخدام Maven (الأفضل)
```bash
mvn clean test                                # تشغيل الاختبارات
mvn jacoco:report                            # تقرير التغطية
mvn org.pitest:pitest-maven:mutationCoverage # اختبار التحور
```

### الطريقة الثانية: باستخدام Java مباشرة
```bash
javac -d . src/main/java/com/testing/*.java  # تجميع الكود
java com.testing.TestRunner                  # تشغيل المثال
```

## 9. الخلاصة

تم إنجاز جميع متطلبات الواجب بنجاح:

✅ **دالة معقدة:** تحتوي على 11 فرع و 2 حلقة  
✅ **اختبارات JUnit:** 6 حالات اختبار شاملة  
✅ **Code Coverage:** إعداد JaCoCo للتغطية الكاملة  
✅ **Mutation Testing:** إعداد PIT لاختبار التحور  
✅ **التوثيق:** شرح مفصل وتعليقات باللغتين  

المشروع جاهز للتسليم ويحقق جميع المعايير المطلوبة لاختبار البرمجيات والجودة.

---

**ملاحظة:** لعرض التقارير المرئية، يجب تشغيل Maven وفتح ملفات HTML الناتجة في المتصفح.
