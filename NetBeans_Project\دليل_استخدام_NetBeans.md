# دليل استخدام مشروع NetBeans - اختب<PERSON>ر البرمجيات والجودة
# NetBeans Project Guide - Software Testing and Quality Assurance

## 📋 نظرة عامة على المشروع

هذا المشروع مصمم خصيصاً لبرنامج **NetBeans IDE** ويحتوي على:

✅ **دالة معقدة** تحتوي على **12+ فرع** و **2 حلقة**  
✅ **8 اختبارات JUnit** شاملة  
✅ **Code Coverage** مدمج في NetBeans  
✅ **إعدادات جاهزة** للـ Mutation Testing  

---

## 🚀 خطوات فتح المشروع في NetBeans

### الخطوة 1: تحميل وتثبيت NetBeans
1. حمل **NetBeans IDE** من: https://netbeans.apache.org/
2. تأكد من تثبيت **Java 11** أو أحدث
3. ثبت NetBeans مع دعم **Java SE**

### الخطوة 2: فتح المشروع
1. افتح NetBeans
2. اذهب إلى **File → Open Project**
3. اختر مجلد `NetBeans_Project/StudentGradeCalculator`
4. اضغط **Open Project**

### الخطوة 3: التحقق من إعدادات المشروع
1. انقر بالزر الأيمن على المشروع
2. اختر **Properties**
3. تأكد من:
   - **Source/Binary Format:** JDK 11 أو أحدث
   - **Libraries:** JUnit 5 مضاف

---

## 🧪 تشغيل الاختبارات

### طريقة 1: تشغيل جميع الاختبارات
1. انقر بالزر الأيمن على المشروع
2. اختر **Test**
3. ستظهر نتائج الاختبارات في نافذة **Test Results**

### طريقة 2: تشغيل اختبار واحد
1. افتح ملف `StudentGradeCalculatorTest.java`
2. انقر بالزر الأيمن على اسم الاختبار
3. اختر **Run Focused Test Method**

### طريقة 3: تشغيل الكود الرئيسي
1. انقر بالزر الأيمن على `StudentGradeCalculator.java`
2. اختر **Run File**
3. ستظهر النتائج في نافذة **Output**

---

## 📊 Code Coverage في NetBeans

### تفعيل Code Coverage:
1. انقر بالزر الأيمن على المشروع
2. اختر **Code Coverage → Show Report**
3. أو اذهب إلى **Window → IDE Tools → Code Coverage**

### تشغيل الاختبارات مع Code Coverage:
1. انقر بالزر الأيمن على المشروع
2. اختر **Test with Code Coverage**
3. ستظهر النتائج في نافذة **Code Coverage**

### قراءة تقرير Code Coverage:
- **خط أخضر:** تم تنفيذه
- **خط أحمر:** لم يتم تنفيذه
- **النسبة المئوية:** تظهر في نافذة Code Coverage

---

## 🔬 Mutation Testing

### الطريقة الأولى: استخدام PIT Plugin
1. اذهب إلى **Tools → Plugins**
2. ابحث عن **PIT** أو **Mutation Testing**
3. ثبت الـ Plugin
4. انقر بالزر الأيمن على المشروع
5. اختر **Run Mutation Tests**

### الطريقة الثانية: استخدام Maven (إضافي)
إذا كنت تريد استخدام Maven مع NetBeans:
1. أنشئ ملف `pom.xml` في جذر المشروع
2. أضف PIT plugin
3. استخدم **Maven Goals** في NetBeans

---

## 📁 هيكل المشروع

```
StudentGradeCalculator/
├── 📁 src/
│   └── 📁 studentgradecalculator/
│       └── 📄 StudentGradeCalculator.java    # الكود الرئيسي ⭐
├── 📁 test/
│   └── 📁 studentgradecalculator/
│       └── 📄 StudentGradeCalculatorTest.java # الاختبارات ⭐
├── 📁 nbproject/
│   ├── 📄 project.xml                        # إعدادات المشروع
│   └── 📄 project.properties                 # خصائص المشروع
├── 📁 build/                                 # ملفات التجميع
├── 📁 dist/                                  # ملفات التوزيع
└── 📄 manifest.mf                            # ملف Manifest
```

---

## 🎯 شرح الكود

### الكلاس الرئيسي: `StudentGradeCalculator`

#### الدالة الرئيسية: `analyzeStudentGrades`
```java
public GradeAnalysisResult analyzeStudentGrades(List<Double> grades, double passingGrade)
```

#### الفروع (12+ فرع):
1. ✅ التحقق من القائمة الفارغة
2. ✅ التحقق من صحة درجة النجاح
3. ✅ التحقق من صحة الدرجة الفردية
4. ✅ تحديد النجاح/الرسوب
5. ✅ تصنيف الطلاب الممتازين (90+)
6. ✅ تصنيف الطلاب الجيدين (80-89)
7. ✅ تصنيف الطلاب المقبولين
8. ✅ تصنيف الطلاب الراسبين
9. ✅ تحديد الأداء الممتاز للفصل (85+)
10. ✅ تحديد الأداء الجيد للفصل (75-84)
11. ✅ تحديد الأداء الضعيف للفصل (<60)
12. ✅ تحديد الأداء المتوسط للفصل

#### الحلقات (2 حلقة):
1. **حلقة for-each:** لحساب الإحصائيات الأساسية
2. **حلقة for:** لتحديد مستوى الأداء العام

---

## 🧪 الاختبارات (8 اختبارات)

1. **testExcellentPerformance** - اختبار الأداء الممتاز
2. **testMixedPerformance** - اختبار الأداء المختلط
3. **testPoorPerformance** - اختبار الأداء الضعيف
4. **testEmptyGradesList** - اختبار القائمة الفارغة
5. **testInvalidPassingGrade** - اختبار درجة النجاح غير الصحيحة
6. **testGradesWithNullValues** - اختبار الدرجات مع قيم null
7. **testAveragePerformance** - اختبار الأداء المتوسط
8. **testOutOfRangeGrades** - اختبار الدرجات خارج النطاق

---

## 📈 النتائج المتوقعة

### اختبارات JUnit:
- **نسبة النجاح:** 100% (8/8 اختبارات)
- **وقت التنفيذ:** أقل من ثانية واحدة

### Code Coverage:
- **تغطية الخطوط:** 100%
- **تغطية الفروع:** 100%
- **تغطية الطرق:** 100%

### Mutation Testing:
- **نسبة قتل المتحورات:** 85%+
- **المتحورات الناجية:** أقل من 15%

---

## 🛠️ حل المشاكل الشائعة

### مشكلة: "JUnit not found"
**الحل:**
1. انقر بالزر الأيمن على المشروع
2. اختر **Properties → Libraries**
3. اضغط **Add Library**
4. اختر **JUnit 5**

### مشكلة: "Package does not exist"
**الحل:**
1. تأكد من أن الملفات في المجلدات الصحيحة
2. انقر بالزر الأيمن على المشروع
3. اختر **Clean and Build**

### مشكلة: "Code Coverage not working"
**الحل:**
1. اذهب إلى **Tools → Options**
2. اختر **Java → Code Coverage**
3. تأكد من تفعيل Code Coverage

### مشكلة: مشاكل في الترميز العربي
**الحل:**
1. اذهب إلى **Tools → Options**
2. اختر **Editor → General**
3. غير **Encoding** إلى **UTF-8**

---

## 📝 خطوات التسليم

### 1. تشغيل جميع الاختبارات
- تأكد من نجاح جميع الاختبارات (8/8)
- خذ لقطة شاشة من نافذة **Test Results**

### 2. إنشاء تقرير Code Coverage
- شغل **Test with Code Coverage**
- خذ لقطة شاشة من نافذة **Code Coverage**
- تأكد من أن التغطية 100%

### 3. تصدير المشروع
- انقر بالزر الأيمن على المشروع
- اختر **Export → To ZIP**
- احفظ الملف المضغوط

### 4. إعداد التقرير النهائي
- اكتب تقرير Word/PDF
- أضف لقطات الشاشة
- أضف شرح الكود والاختبارات
- أرفق الملف المضغوط للمشروع

---

## 🏆 نصائح للنجاح

1. **اقرأ الكود بعناية** - تأكد من فهم جميع الفروع والحلقات
2. **شغل الاختبارات عدة مرات** - للتأكد من الاستقرار
3. **راجع تقرير Code Coverage** - تأكد من تغطية جميع الخطوط
4. **وثق كل شيء** - خذ لقطات شاشة لكل خطوة
5. **اختبر على أجهزة مختلفة** - للتأكد من عمل المشروع

---

**المشروع جاهز للاستخدام في NetBeans! 🎉**
