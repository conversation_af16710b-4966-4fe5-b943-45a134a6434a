@echo off
echo ========================================
echo تشغيل سريع لمشروع NetBeans
echo Quick Run for NetBeans Project
echo ========================================

echo.
echo 1. تجميع الكود...
echo Compiling code...

javac -d . src/studentgradecalculator/*.java

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في التجميع!
    echo Compilation error!
    pause
    exit /b 1
)

echo تم التجميع بنجاح!
echo Compilation successful!

echo.
echo 2. تشغيل الكود الرئيسي...
echo Running main code...

java studentgradecalculator.StudentGradeCalculator

echo.
echo ========================================
echo تم الانتهاء من التشغيل!
echo Execution completed!
echo ========================================
echo.
echo لتشغيل الاختبارات في NetBeans:
echo To run tests in NetBeans:
echo 1. افتح NetBeans - Open NetBeans
echo 2. File → Open Project
echo 3. اختر هذا المجلد - Select this folder
echo 4. انقر بالزر الأيمن → Test
echo.
pause
