@echo off
echo ========================================
echo تجميع وتشغيل مشروع اختبار البرمجيات
echo Software Testing Project Compilation
echo ========================================

REM إنشاء مجلدات الإخراج
mkdir target\classes 2>nul
mkdir target\test-classes 2>nul

echo.
echo 1. تجميع الكلاس الرئيسي...
echo Compiling main class...

REM تجميع الكلاس الرئيسي
javac -d target\classes src\main\java\com\testing\*.java

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في تجميع الكلاس الرئيسي!
    echo Error compiling main class!
    pause
    exit /b 1
)

echo تم تجميع الكلاس الرئيسي بنجاح!
echo Main class compiled successfully!

echo.
echo 2. تشغيل مثال بسيط...
echo Running simple example...

REM إنشاء ملف Java بسيط لتشغيل المثال
echo package com.testing; > target\TestRunner.java
echo import java.util.*; >> target\TestRunner.java
echo public class TestRunner { >> target\TestRunner.java
echo     public static void main(String[] args) { >> target\TestRunner.java
echo         StudentGradeCalculator calculator = new StudentGradeCalculator(); >> target\TestRunner.java
echo         List^<Double^> grades = Arrays.asList(95.0, 85.0, 75.0, 65.0, 45.0); >> target\TestRunner.java
echo         StudentGradeCalculator.GradeAnalysisResult result = >> target\TestRunner.java
echo             calculator.analyzeStudentGrades(grades, 60.0); >> target\TestRunner.java
echo         System.out.println("=== نتائج تحليل الدرجات ==="); >> target\TestRunner.java
echo         System.out.println(result.toString()); >> target\TestRunner.java
echo         System.out.println("========================="); >> target\TestRunner.java
echo     } >> target\TestRunner.java
echo } >> target\TestRunner.java

REM تجميع وتشغيل المثال
javac -cp target\classes -d target target\TestRunner.java
java -cp target;target\classes com.testing.TestRunner

echo.
echo ========================================
echo تم الانتهاء من تشغيل المثال!
echo Example execution completed!
echo ========================================
echo.
echo لتشغيل الاختبارات، تحتاج إلى:
echo To run tests, you need:
echo 1. تثبيت Maven - Install Maven
echo 2. تشغيل: mvn test - Run: mvn test
echo 3. تشغيل: mvn jacoco:report - Run: mvn jacoco:report
echo 4. تشغيل: mvn org.pitest:pitest-maven:mutationCoverage
echo.
pause
