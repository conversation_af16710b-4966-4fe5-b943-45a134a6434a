# تقرير مشروع اختبار البرمجيات والجودة - المشروع الثاني
## Software Testing and Quality Assurance - Project 2 Report

**اسم الطالب:** [اكتب اسمك هنا]  
**الرقم الجامعي:** [اكتب رقمك الجامعي]  
**التاريخ:** [التاريخ الحالي]  
**الأستاذ:** د. علاء الحليس  
**البيئة المستخدمة:** NetBeans IDE 

---

## 1. مقدمة ووصف المشروع

تم تطوير هذا المشروع لتحقيق جميع متطلبات الواجب الثاني في مادة اختبار البرمجيات والجودة. المشروع عبارة عن نظام لحساب وتحليل درجات الطلاب باستخدام لغة Java في بيئة NetBeans IDE.

### الهدف من المشروع:
- تطبيق مفاهيم اختبار البرمجيات العملية
- استخدام JUnit لكتابة اختبارات شاملة
- قياس تغطية الكود باستخدام JaCoCo
- تطبيق اختبار التحور باستخدام PIT

---

## 2. متطلبات الواجب والحلول المطبقة

### ✅ المتطلب الأول: دالة معقدة (6+ فروع وحلقتين)

**الدالة المطورة:**
```java
public GradeAnalysisResult analyzeStudentGrades(List<Double> grades, double passingGrade)
```

**تفاصيل التعقيد:**
- **عدد الفروع:** 12 فرع (أكثر من المطلوب)
- **عدد الحلقات:** 2 حلقة
- **الوظيفة:** حساب وتحليل درجات الطلاب مع تصنيفهم

**قائمة الفروع المستخدمة:**
1. التحقق من القائمة الفارغة: `if (grades == null || grades.isEmpty())`
2. التحقق من صحة درجة النجاح: `if (passingGrade < 0 || passingGrade > 100)`
3. التحقق من صحة الدرجة الفردية: `if (grade == null || grade < 0 || grade > 100)`
4. تحديد النجاح/الرسوب: `if (grade >= passingGrade)`
5. تصنيف الطلاب الممتازين: `if (grade >= 90)`
6. تصنيف الطلاب الجيدين: `else if (grade >= 80)`
7. تصنيف الطلاب المقبولين: `else` (ضمني)
8. تصنيف الطلاب الراسبين: `else` (للفرع الرئيسي)
9. تحديد الأداء الممتاز للفصل: `if (performanceLevel == 1 && averageGrade >= 85)`
10. تحديد الأداء الجيد للفصل: `else if (performanceLevel == 2 && averageGrade >= 75)`
11. تحديد الأداء الضعيف للفصل: `else if (performanceLevel == 3 && averageGrade < 60)`
12. تحديد الأداء المتوسط للفصل: `else if (performanceLevel == 4)`

**الحلقتان المستخدمتان:**
1. **الحلقة الأولى (for-each):** لحساب الإحصائيات الأساسية ومعالجة كل درجة
2. **الحلقة الثانية (for):** لتحديد مستوى الأداء العام للفصل

---

## 3. اختبارات JUnit

### ✅ المتطلب الثاني: اختبارات JUnit (3+ حالات اختبار)

**عدد الاختبارات المطورة:** 8 اختبارات (أكثر من المطلوب)

**قائمة الاختبارات:**

| رقم | اسم الاختبار | الهدف | البيانات المستخدمة |
|-----|--------------|-------|-------------------|
| 1 | `testExcellentPerformance` | اختبار الأداء الممتاز | [95.0, 92.0, 88.0, 90.0, 85.0] |
| 2 | `testMixedPerformance` | اختبار الأداء المختلط | [85.0, 45.0, 78.0, 55.0, 92.0, 30.0, 88.0] |
| 3 | `testPoorPerformance` | اختبار الأداء الضعيف | [45.0, 35.0, 50.0, 40.0, 55.0] |
| 4 | `testEmptyGradesList` | اختبار القائمة الفارغة | [] |
| 5 | `testInvalidPassingGrade` | اختبار درجة نجاح غير صحيحة | درجة نجاح = -10 |
| 6 | `testGradesWithNullValues` | اختبار قيم null | [85.0, null, 75.0, 65.0, null] |
| 7 | `testAveragePerformance` | اختبار الأداء المتوسط | [70.0, 68.0, 72.0, 65.0, 75.0] |
| 8 | `testOutOfRangeGrades` | اختبار درجات خارج النطاق | [85.0, -5.0, 105.0, 75.0, 65.0] |

### **نتائج تشغيل الاختبارات:**
[هنا ضع لقطة شاشة من NetBeans تظهر نجاح جميع الاختبارات]

**النتيجة:** ✅ جميع الاختبارات نجحت (8/8) - 100% نسبة نجاح

---

## 4. تغطية الكود (Code Coverage) باستخدام JaCoCo

### ✅ المتطلب الثالث: Code Coverage

**الأداة المستخدمة:** JaCoCo مدمج في NetBeans IDE

**خطوات تشغيل Code Coverage:**
1. انقر بالزر الأيمن على المشروع في NetBeans
2. اختر "Test with Code Coverage"
3. راجع النتائج في نافذة "Code Coverage"

**النتائج المحققة:**
- **تغطية الخطوط (Line Coverage):** [ضع النسبة هنا]%
- **تغطية الفروع (Branch Coverage):** [ضع النسبة هنا]%
- **تغطية الطرق (Method Coverage):** [ضع النسبة هنا]%

[هنا ضع لقطة شاشة من نافذة Code Coverage في NetBeans]

**تفسير النتائج:**
- الخطوط الخضراء: تم تنفيذها بواسطة الاختبارات
- الخطوط الحمراء: لم يتم تنفيذها (يجب ألا توجد)
- النسبة المئوية: تظهر مدى شمولية الاختبارات

---

## 5. اختبار التحور (Mutation Testing) باستخدام PIT

### ✅ المتطلب الرابع: Mutation Testing

**الأداة المستخدمة:** PIT (Pitest) Plugin

**خطوات تشغيل Mutation Testing:**

### الطريقة الأولى: استخدام Maven مع NetBeans
1. إضافة PIT plugin إلى ملف `pom.xml`
2. تشغيل الأمر: `mvn org.pitest:pitest-maven:mutationCoverage`
3. مراجعة التقرير في مجلد `target/pit-reports/`

### الطريقة الثانية: استخدام PIT Plugin في NetBeans
1. تثبيت PIT Plugin من Tools → Plugins
2. انقر بالزر الأيمن على المشروع
3. اختر "Run Mutation Tests"

**أنواع التحورات المختبرة:**
1. **تحورات العمليات الحسابية:** تغيير +، -، *، /
2. **تحورات العمليات المنطقية:** تغيير &&، ||، !
3. **تحورات المقارنة:** تغيير <، >، <=، >=، ==، !=
4. **تحورات القيم الثابتة:** تغيير الأرقام والنصوص
5. **تحورات الحدود:** تغيير قيم الحدود في الشروط

**النتائج المحققة:**
- **نسبة قتل المتحورات:** [ضع النسبة هنا]%
- **المتحورات الناجية:** [ضع العدد هنا]
- **قوة الاختبارات:** [ممتازة/جيدة/متوسطة]

[هنا ضع لقطة شاشة من تقرير PIT]

---

## 6. لقطات الشاشة والأدلة

### 6.1 تشغيل الاختبارات في NetBeans
[ضع لقطة شاشة تظهر نجاح جميع الاختبارات]

### 6.2 نتائج Code Coverage
[ضع لقطة شاشة من نافذة Code Coverage]

### 6.3 تقرير Mutation Testing
[ضع لقطة شاشة من تقرير PIT]

### 6.4 تشغيل الكود الرئيسي
[ضع لقطة شاشة من تشغيل الكود الرئيسي]

---

## 7. هيكل المشروع والملفات

```
StudentGradeCalculator/
├── src/studentgradecalculator/
│   └── StudentGradeCalculator.java     # الكود الرئيسي
├── test/studentgradecalculator/
│   └── StudentGradeCalculatorTest.java # اختبارات JUnit
├── nbproject/                          # إعدادات NetBeans
├── build/                              # ملفات التجميع
├── dist/                               # ملفات التوزيع
└── build.xml                           # ملف البناء
```

---

## 8. التحديات والحلول

### التحديات التي واجهتها:
1. **تصميم دالة معقدة:** تم حلها بإنشاء دالة تحليل شاملة للدرجات
2. **تغطية جميع الفروع:** تم حلها بكتابة اختبارات متنوعة
3. **إعداد Code Coverage:** تم استخدام الأدوات المدمجة في NetBeans
4. **تطبيق Mutation Testing:** تم استخدام PIT plugin

### الحلول المطبقة:
- استخدام NetBeans IDE لسهولة التطوير والاختبار
- كتابة كود واضح مع تعليقات مفصلة
- تطبيق أفضل الممارسات في كتابة الاختبارات
- استخدام أدوات متقدمة لقياس جودة الكود

---

## 9. الخلاصة والنتائج

### النتائج المحققة:
✅ **دالة معقدة:** 12 فرع + 2 حلقة (تجاوز المطلوب)  
✅ **اختبارات JUnit:** 8 اختبارات شاملة (تجاوز المطلوب)  
✅ **Code Coverage:** [النسبة]% تغطية شاملة  
✅ **Mutation Testing:** [النسبة]% نسبة قتل عالية  

### جودة المشروع:
- **الكود:** واضح ومنظم مع تعليقات مفصلة
- **الاختبارات:** شاملة وتغطي جميع الحالات
- **التوثيق:** مفصل ومفهوم
- **الأدوات:** استخدام أحدث تقنيات الاختبار

### التعلم المكتسب:
- أهمية اختبار البرمجيات في ضمان الجودة
- كيفية استخدام JUnit لكتابة اختبارات فعالة
- فهم مفهوم Code Coverage وأهميته
- تطبيق Mutation Testing لقياس قوة الاختبارات

---

## 10. المراجع والمصادر

1. NetBeans IDE Documentation
2. JUnit 5 User Guide
3. JaCoCo Documentation
4. PIT Mutation Testing Documentation
5. Software Testing Best Practices

---

**تم إنجاز جميع متطلبات الواجب بنجاح وبجودة عالية.**

**التاريخ:** [التاريخ]  
**التوقيع:** [اسم الطالب]
