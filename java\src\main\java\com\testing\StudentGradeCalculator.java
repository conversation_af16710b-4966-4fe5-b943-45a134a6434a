package com.testing;

import java.util.List;
import java.util.ArrayList;

/**
 * فئة لحساب درجات الطلاب وتصنيفهم
 * Student Grade Calculator Class
 * 
 * هذه الفئة تحتوي على دالة معقدة تحتوي على أكثر من 6 فروع وحلقتين
 * This class contains a complex method with more than 6 branches and two loops
 */
public class StudentGradeCalculator {

    /**
     * دالة لحساب وتصنيف درجات الطلاب
     * Method to calculate and classify student grades
     * 
     * @param grades قائمة بدرجات الطلاب (List of student grades)
     * @param passingGrade الدرجة المطلوبة للنجاح (Minimum passing grade)
     * @return نتيجة التحليل (Analysis result)
     */
    public GradeAnalysisResult analyzeStudentGrades(List<Double> grades, double passingGrade) {
        // التحقق من صحة المدخلات (Input validation)
        if (grades == null || grades.isEmpty()) {
            return new GradeAnalysisResult("خطأ: قائمة الدرجات فارغة", 0, 0, 0, 0, 0);
        }
        
        if (passingGrade < 0 || passingGrade > 100) {
            return new GradeAnalysisResult("خطأ: درجة النجاح غير صحيحة", 0, 0, 0, 0, 0);
        }

        int totalStudents = grades.size();
        int passedStudents = 0;
        int failedStudents = 0;
        int excellentStudents = 0; // 90+ درجة ممتاز
        int goodStudents = 0;      // 80-89 درجة جيد
        double totalGrades = 0;
        
        // الحلقة الأولى: حساب الإحصائيات الأساسية (First loop: Calculate basic statistics)
        for (Double grade : grades) {
            // التحقق من صحة الدرجة (Validate grade)
            if (grade == null || grade < 0 || grade > 100) {
                continue; // تجاهل الدرجات غير الصحيحة (Skip invalid grades)
            }
            
            totalGrades += grade;
            
            // فرع 1: تحديد النجاح أو الرسوب (Branch 1: Pass/Fail determination)
            if (grade >= passingGrade) {
                passedStudents++;
                
                // فرع 2: تصنيف الطلاب الناجحين (Branch 2: Classification of passed students)
                if (grade >= 90) {
                    excellentStudents++; // فرع 3: ممتاز (Branch 3: Excellent)
                } else if (grade >= 80) {
                    goodStudents++; // فرع 4: جيد (Branch 4: Good)
                }
                // فرع 5: مقبول (أقل من 80 لكن أكبر من درجة النجاح) (Branch 5: Acceptable)
            } else {
                failedStudents++; // فرع 6: راسب (Branch 6: Failed)
            }
        }
        
        double averageGrade = totalStudents > 0 ? totalGrades / totalStudents : 0;
        
        // الحلقة الثانية: تحديد التصنيف العام للفصل (Second loop: Determine class overall performance)
        String classPerformance = "متوسط";
        int highPerformers = excellentStudents + goodStudents;
        
        for (int i = 0; i < 3; i++) { // حلقة للتحقق من مستويات الأداء (Loop to check performance levels)
            if (i == 0 && averageGrade >= 85) {
                classPerformance = "ممتاز"; // فرع 7: أداء ممتاز (Branch 7: Excellent performance)
                break;
            } else if (i == 1 && averageGrade >= 75) {
                classPerformance = "جيد"; // فرع 8: أداء جيد (Branch 8: Good performance)
                break;
            } else if (i == 2 && averageGrade < 60) {
                classPerformance = "ضعيف"; // فرع 9: أداء ضعيف (Branch 9: Poor performance)
                break;
            }
        }
        
        return new GradeAnalysisResult(
            classPerformance,
            totalStudents,
            passedStudents,
            failedStudents,
            averageGrade,
            excellentStudents
        );
    }
    
    /**
     * فئة لتخزين نتائج تحليل الدرجات
     * Class to store grade analysis results
     */
    public static class GradeAnalysisResult {
        private String classPerformance;
        private int totalStudents;
        private int passedStudents;
        private int failedStudents;
        private double averageGrade;
        private int excellentStudents;
        
        public GradeAnalysisResult(String classPerformance, int totalStudents, 
                                 int passedStudents, int failedStudents, 
                                 double averageGrade, int excellentStudents) {
            this.classPerformance = classPerformance;
            this.totalStudents = totalStudents;
            this.passedStudents = passedStudents;
            this.failedStudents = failedStudents;
            this.averageGrade = averageGrade;
            this.excellentStudents = excellentStudents;
        }
        
        // Getters
        public String getClassPerformance() { return classPerformance; }
        public int getTotalStudents() { return totalStudents; }
        public int getPassedStudents() { return passedStudents; }
        public int getFailedStudents() { return failedStudents; }
        public double getAverageGrade() { return averageGrade; }
        public int getExcellentStudents() { return excellentStudents; }
        
        @Override
        public String toString() {
            return String.format(
                "تحليل الدرجات:\n" +
                "الأداء العام: %s\n" +
                "إجمالي الطلاب: %d\n" +
                "الطلاب الناجحون: %d\n" +
                "الطلاب الراسبون: %d\n" +
                "المتوسط العام: %.2f\n" +
                "الطلاب المتفوقون: %d",
                classPerformance, totalStudents, passedStudents, 
                failedStudents, averageGrade, excellentStudents
            );
        }
    }
}
