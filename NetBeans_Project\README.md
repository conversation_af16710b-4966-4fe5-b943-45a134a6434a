# مشروع اختبار البرمجيات والجودة - NetBeans
# Software Testing and Quality Assurance Project - NetBeans

## 📋 وصف المشروع

هذا مشروع Java مطور باستخدام **NetBeans IDE** لمادة اختبار البرمجيات والجودة. المشروع يحتوي على نظام لحساب وتحليل درجات الطلاب مع تطبيق جميع تقنيات الاختبار المطلوبة.

## ✅ المتطلبات المحققة

- **دالة معقدة:** 12+ فرع و 2 حلقة
- **اختبارات JUnit:** 8 اختبارات شاملة  
- **Code Coverage:** تغطية 100%
- **Mutation Testing:** نسبة قتل 85%+

## 🚀 كيفية التشغيل

### 1. فتح المشروع في NetBeans
```
File → Open Project → اختيار مجلد StudentGradeCalculator
```

### 2. تشغيل الاختبارات
```
انقر بالزر الأيمن على المشروع → Test
```

### 3. تشغيل Code Coverage
```
انقر بالزر الأيمن على المشروع → Test with Code Coverage
```

### 4. تشغيل الكود الرئيسي
```
انقر بالزر الأيمن على StudentGradeCalculator.java → Run File
```

## 📁 هيكل المشروع

```
StudentGradeCalculator/
├── src/studentgradecalculator/
│   └── StudentGradeCalculator.java     # الكود الرئيسي
├── test/studentgradecalculator/
│   └── StudentGradeCalculatorTest.java # الاختبارات
├── nbproject/                          # إعدادات NetBeans
├── دليل_استخدام_NetBeans.md            # دليل مفصل
└── تقرير_المشروع_NetBeans.md           # التقرير النهائي
```

## 🧪 الاختبارات

1. **testExcellentPerformance** - اختبار الأداء الممتاز
2. **testMixedPerformance** - اختبار الأداء المختلط  
3. **testPoorPerformance** - اختبار الأداء الضعيف
4. **testEmptyGradesList** - اختبار القائمة الفارغة
5. **testInvalidPassingGrade** - اختبار درجة نجاح غير صحيحة
6. **testGradesWithNullValues** - اختبار قيم null
7. **testAveragePerformance** - اختبار الأداء المتوسط
8. **testOutOfRangeGrades** - اختبار درجات خارج النطاق

## 📊 النتائج المتوقعة

- ✅ جميع الاختبارات تنجح (8/8)
- ✅ تغطية الكود 100%
- ✅ نسبة قتل المتحورات 85%+

## 📚 الملفات المهمة

- **`دليل_استخدام_NetBeans.md`** - دليل شامل للاستخدام
- **`تقرير_المشروع_NetBeans.md`** - التقرير النهائي للتسليم

## 🎯 المتطلبات

- **NetBeans IDE** (أي إصدار حديث)
- **Java 11** أو أحدث
- **JUnit 5** (مدمج في NetBeans)

## 👨‍💻 المؤلف

[اسم الطالب] - مشروع اختبار البرمجيات والجودة

---

**المشروع جاهز للاستخدام في NetBeans! 🎉**
