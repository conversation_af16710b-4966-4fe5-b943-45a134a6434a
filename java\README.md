# مشروع اختبار البرمجيات والجودة - المشروع الثاني
# Software Testing and Quality Assurance - Project 2

## وصف المشروع (Project Description)

هذا المشروع يحتوي على تطبيق Java بسيط لحساب وتحليل درجات الطلاب، مع تطبيق جميع متطلبات الواجب:

This project contains a simple Java application for calculating and analyzing student grades, implementing all assignment requirements:

### المتطلبات المحققة (Requirements Met):

1. **دالة تحتوي على 6 فروع وحلقتين على الأقل**
   - الدالة `analyzeStudentGrades` تحتوي على 9 فروع و 2 حلقات
   - Method `analyzeStudentGrades` contains 9 branches and 2 loops

2. **اختبارات JUnit (6 حالات اختبار)**
   - اختبار الأداء الممتاز
   - اختبار الأداء المختلط
   - اختبار الأداء الضعيف
   - اختبار القائمة الفارغة
   - اختبار درجة النجاح غير الصحيحة
   - اختبار الدرجات مع قيم null

3. **تغطية الكود (Code Coverage) باستخدام JaCoCo**

4. **اختبار التحور (Mutation Testing) باستخدام PIT**

## هيكل المشروع (Project Structure)

```
java/
├── pom.xml                                    # Maven configuration
├── src/
│   ├── main/java/com/testing/
│   │   └── StudentGradeCalculator.java       # الكلاس الرئيسي
│   └── test/java/com/testing/
│       └── StudentGradeCalculatorTest.java   # فئة الاختبارات
└── README.md                                 # هذا الملف
```

## شرح الكود (Code Explanation)

### الكلاس الرئيسي: StudentGradeCalculator

يحتوي على دالة `analyzeStudentGrades` التي:
- تتلقى قائمة بدرجات الطلاب ودرجة النجاح المطلوبة
- تحتوي على **9 فروع** مختلفة للتحكم في التدفق
- تحتوي على **حلقتين**: واحدة لحساب الإحصائيات وأخرى لتحديد الأداء العام
- ترجع نتيجة تحليل شاملة

### الفروع التسعة (9 Branches):
1. التحقق من القائمة الفارغة
2. التحقق من صحة درجة النجاح
3. تحديد النجاح/الرسوب
4. تصنيف الطلاب الممتازين (90+)
5. تصنيف الطلاب الجيدين (80-89)
6. تصنيف الطلاب المقبولين
7. تصنيف الطلاب الراسبين
8. تحديد الأداء الممتاز للفصل
9. تحديد الأداء الجيد للفصل
10. تحديد الأداء الضعيف للفصل

### الحلقتان (2 Loops):
1. **الحلقة الأولى**: تمر عبر جميع الدرجات لحساب الإحصائيات
2. **الحلقة الثانية**: تحدد مستوى الأداء العام للفصل

## كيفية تشغيل المشروع (How to Run)

### 1. تشغيل الاختبارات (Run Tests)
```bash
mvn test
```

### 2. إنشاء تقرير تغطية الكود (Generate Code Coverage Report)
```bash
mvn jacoco:report
```
التقرير سيكون في: `target/site/jacoco/index.html`

### 3. تشغيل اختبار التحور (Run Mutation Testing)
```bash
mvn org.pitest:pitest-maven:mutationCoverage
```
التقرير سيكون في: `target/pit-reports/`

### 4. تشغيل جميع الاختبارات مع التقارير
```bash
mvn clean test jacoco:report org.pitest:pitest-maven:mutationCoverage
```

## النتائج المتوقعة (Expected Results)

- **تغطية الكود**: يجب أن تكون 100% لجميع الفروع والخطوط
- **اختبار التحور**: يجب أن تقتل الاختبارات معظم المتحورات
- **جميع الاختبارات**: يجب أن تنجح جميع الاختبارات الستة

## الأدوات المستخدمة (Tools Used)

- **Java 11**: لغة البرمجة
- **Maven**: إدارة المشروع والتبعيات
- **JUnit 5**: إطار عمل الاختبارات
- **JaCoCo**: قياس تغطية الكود
- **PIT**: اختبار التحور

## ملاحظات مهمة (Important Notes)

1. الكود مكتوب بطريقة بسيطة ومفهومة
2. جميع التعليقات باللغتين العربية والإنجليزية
3. الاختبارات تغطي جميع الحالات الممكنة
4. المشروع جاهز للتشغيل مباشرة
