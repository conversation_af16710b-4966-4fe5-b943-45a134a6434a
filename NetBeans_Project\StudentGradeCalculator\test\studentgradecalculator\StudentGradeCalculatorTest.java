package studentgradecalculator;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

public class StudentGradeCalculatorTest {
    
    private StudentGradeCalculator calculator;
    
    @BeforeEach
    void setUp() {
        calculator = new StudentGradeCalculator();
        System.out.println("=== بدء اختبار جديد ===");
    }
    
    @Test
    @DisplayName("اختبار الأداء الممتاز - Test Excellent Performance")
    void testExcellentPerformance() {
        System.out.println("تشغيل اختبار الأداء الممتاز...");
        List<Double> excellentGrades = Arrays.asList(95.0, 92.0, 88.0, 90.0, 85.0);
        double passingGrade = 60.0;
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(excellentGrades, passingGrade);
        assertEquals("ممتاز", result.getClassPerformance());
        assertEquals(5, result.getTotalStudents());
        assertEquals(5, result.getPassedStudents());
        assertEquals(0, result.getFailedStudents());
        assertEquals(90.0, result.getAverageGrade(), 0.01);
        assertEquals(3, result.getExcellentStudents());
        assertEquals(1, result.getGoodStudents());
        assertEquals(1, result.getAcceptableStudents());
        System.out.println("✓ اختبار الأداء الممتاز نجح");
        System.out.println(result.toString());
    }
    
    @Test
    @DisplayName("اختبار الأداء المختلط - Test Mixed Performance")
    void testMixedPerformance() {
        System.out.println("تشغيل اختبار الأداء المختلط...");
        List<Double> mixedGrades = Arrays.asList(85.0, 45.0, 78.0, 55.0, 92.0, 30.0, 88.0);
        double passingGrade = 60.0;
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(mixedGrades, passingGrade);
        assertEquals("جيد", result.getClassPerformance());
        assertEquals(7, result.getTotalStudents());
        assertEquals(4, result.getPassedStudents());
        assertEquals(3, result.getFailedStudents());
        assertEquals(67.57, result.getAverageGrade(), 0.01);
        assertEquals(1, result.getExcellentStudents());
        assertEquals(2, result.getGoodStudents());
        assertEquals(1, result.getAcceptableStudents());
        System.out.println("✓ اختبار الأداء المختلط نجح");
        System.out.println(result.toString());
    }
    
    @Test
    @DisplayName("اختبار الأداء الضعيف - Test Poor Performance")
    void testPoorPerformance() {
        System.out.println("تشغيل اختبار الأداء الضعيف...");
        List<Double> poorGrades = Arrays.asList(45.0, 35.0, 50.0, 40.0, 55.0);
        double passingGrade = 60.0;
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(poorGrades, passingGrade);
        assertEquals("ضعيف", result.getClassPerformance());
        assertEquals(5, result.getTotalStudents());
        assertEquals(0, result.getPassedStudents());
        assertEquals(5, result.getFailedStudents());
        assertEquals(45.0, result.getAverageGrade(), 0.01);
        assertEquals(0, result.getExcellentStudents());
        assertEquals(0, result.getGoodStudents());
        assertEquals(0, result.getAcceptableStudents());
        System.out.println("✓ اختبار الأداء الضعيف نجح");
        System.out.println(result.toString());
    }
    
    @Test
    @DisplayName("اختبار القائمة الفارغة - Test Empty List")
    void testEmptyGradesList() {
        System.out.println("تشغيل اختبار القائمة الفارغة...");
        List<Double> emptyGrades = new ArrayList<>();
        double passingGrade = 60.0;
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(emptyGrades, passingGrade);
        assertTrue(result.getClassPerformance().contains("خطأ"));
        assertEquals(0, result.getTotalStudents());
        System.out.println("✓ اختبار القائمة الفارغة نجح");
        System.out.println(result.toString());
    }
    
    @Test
    @DisplayName("اختبار درجة النجاح غير الصحيحة - Test Invalid Passing Grade")
    void testInvalidPassingGrade() {
        System.out.println("تشغيل اختبار درجة النجاح غير الصحيحة...");
        List<Double> grades = Arrays.asList(85.0, 75.0, 65.0);
        double invalidPassingGrade = -10.0;
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(grades, invalidPassingGrade);
        assertTrue(result.getClassPerformance().contains("خطأ"));
        System.out.println("✓ اختبار درجة النجاح غير الصحيحة نجح");
        System.out.println(result.toString());
    }
    
    @Test
    @DisplayName("اختبار الدرجات مع قيم null - Test Grades with Null Values")
    void testGradesWithNullValues() {
        System.out.println("تشغيل اختبار الدرجات مع قيم null...");
        List<Double> gradesWithNull = Arrays.asList(85.0, null, 75.0, 65.0, null);
        double passingGrade = 60.0;
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(gradesWithNull, passingGrade);
        assertEquals(5, result.getTotalStudents());
        assertEquals(3, result.getPassedStudents());
        assertEquals(75.0, result.getAverageGrade(), 0.01);
        System.out.println("✓ اختبار الدرجات مع قيم null نجح");
        System.out.println(result.toString());
    }
    
    @Test
    @DisplayName("اختبار الأداء المتوسط - Test Average Performance")
    void testAveragePerformance() {
        System.out.println("تشغيل اختبار الأداء المتوسط...");
        List<Double> averageGrades = Arrays.asList(70.0, 68.0, 72.0, 65.0, 75.0);
        double passingGrade = 60.0;
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(averageGrades, passingGrade);
        assertEquals("متوسط", result.getClassPerformance());
        assertEquals(5, result.getTotalStudents());
        assertEquals(5, result.getPassedStudents());
        assertEquals(0, result.getFailedStudents());
        assertEquals(70.0, result.getAverageGrade(), 0.01);
        System.out.println("✓ اختبار الأداء المتوسط نجح");
        System.out.println(result.toString());
    }
    
    @Test
    @DisplayName("اختبار الدرجات خارج النطاق - Test Out of Range Grades")
    void testOutOfRangeGrades() {
        System.out.println("تشغيل اختبار الدرجات خارج النطاق...");
        List<Double> invalidGrades = Arrays.asList(85.0, -5.0, 105.0, 75.0, 65.0);
        double passingGrade = 60.0;
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(invalidGrades, passingGrade);
        assertEquals(5, result.getTotalStudents());
        assertEquals(3, result.getPassedStudents());
        assertEquals(75.0, result.getAverageGrade(), 0.01);
        System.out.println("✓ اختبار الدرجات خارج النطاق نجح");
        System.out.println(result.toString());
    }
}
