package studentgradecalculator;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

/**
 * فئة اختبار لفئة حساب درجات الطلاب
 * Test class for StudentGradeCalculator
 * 
 * <AUTHOR> الطالب]
 * تحتوي على 6+ حالات اختبار لتغطية جميع الفروع والحلقات
 * Contains 6+ test cases to cover all branches and loops
 */
public class StudentGradeCalculatorTest {
    
    private StudentGradeCalculator calculator;
    
    @BeforeEach
    void setUp() {
        calculator = new StudentGradeCalculator();
        System.out.println("=== بدء اختبار جديد ===");
    }
    
    /**
     * اختبار 1: اختبار حالة الأداء الممتاز
     * Test 1: Test excellent performance case
     * يختبر الفروع: النجاح، التصنيف الممتاز، الأداء الممتاز للفصل
     */
    @Test
    @DisplayName("اختبار الأداء الممتاز - Test Excellent Performance")
    void testExcellentPerformance() {
        System.out.println("تشغيل اختبار الأداء الممتاز...");
        
        // ترتيب البيانات (Arrange)
        List<Double> excellentGrades = Arrays.asList(95.0, 92.0, 88.0, 90.0, 85.0);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(excellentGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals("ممتاز", result.getClassPerformance());
        assertEquals(5, result.getTotalStudents());
        assertEquals(5, result.getPassedStudents());
        assertEquals(0, result.getFailedStudents());
        assertEquals(90.0, result.getAverageGrade(), 0.01);
        assertEquals(3, result.getExcellentStudents()); // 95, 92, 90
        assertEquals(1, result.getGoodStudents()); // 88
        assertEquals(1, result.getAcceptableStudents()); // 85
        
        System.out.println("✓ اختبار الأداء الممتاز نجح");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 2: اختبار حالة الأداء المختلط (ناجح وراسب)
     * Test 2: Test mixed performance case (pass and fail)
     * يختبر الفروع: النجاح، الرسوب، التصنيفات المختلفة، الأداء الجيد
     */
    @Test
    @DisplayName("اختبار الأداء المختلط - Test Mixed Performance")
    void testMixedPerformance() {
        System.out.println("تشغيل اختبار الأداء المختلط...");
        
        // ترتيب البيانات (Arrange)
        List<Double> mixedGrades = Arrays.asList(85.0, 45.0, 78.0, 55.0, 92.0, 30.0, 88.0);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(mixedGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals("جيد", result.getClassPerformance());
        assertEquals(7, result.getTotalStudents());
        assertEquals(4, result.getPassedStudents()); // 85, 78, 92, 88
        assertEquals(3, result.getFailedStudents());  // 45, 55, 30
        assertEquals(67.57, result.getAverageGrade(), 0.01);
        assertEquals(1, result.getExcellentStudents()); // 92
        assertEquals(2, result.getGoodStudents()); // 85, 88
        assertEquals(1, result.getAcceptableStudents()); // 78
        
        System.out.println("✓ اختبار الأداء المختلط نجح");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 3: اختبار حالة الأداء الضعيف
     * Test 3: Test poor performance case
     * يختبر الفروع: الرسوب، الأداء الضعيف للفصل
     */
    @Test
    @DisplayName("اختبار الأداء الضعيف - Test Poor Performance")
    void testPoorPerformance() {
        System.out.println("تشغيل اختبار الأداء الضعيف...");
        
        // ترتيب البيانات (Arrange)
        List<Double> poorGrades = Arrays.asList(45.0, 35.0, 50.0, 40.0, 55.0);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(poorGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals("ضعيف", result.getClassPerformance());
        assertEquals(5, result.getTotalStudents());
        assertEquals(0, result.getPassedStudents());
        assertEquals(5, result.getFailedStudents());
        assertEquals(45.0, result.getAverageGrade(), 0.01);
        assertEquals(0, result.getExcellentStudents());
        assertEquals(0, result.getGoodStudents());
        assertEquals(0, result.getAcceptableStudents());
        
        System.out.println("✓ اختبار الأداء الضعيف نجح");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 4: اختبار حالة القائمة الفارغة
     * Test 4: Test empty list case
     * يختبر فرع التحقق من القائمة الفارغة
     */
    @Test
    @DisplayName("اختبار القائمة الفارغة - Test Empty List")
    void testEmptyGradesList() {
        System.out.println("تشغيل اختبار القائمة الفارغة...");
        
        // ترتيب البيانات (Arrange)
        List<Double> emptyGrades = new ArrayList<>();
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(emptyGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertTrue(result.getClassPerformance().contains("خطأ"));
        assertEquals(0, result.getTotalStudents());
        
        System.out.println("✓ اختبار القائمة الفارغة نجح");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 5: اختبار درجة نجاح غير صحيحة
     * Test 5: Test invalid passing grade
     * يختبر فرع التحقق من صحة درجة النجاح
     */
    @Test
    @DisplayName("اختبار درجة النجاح غير الصحيحة - Test Invalid Passing Grade")
    void testInvalidPassingGrade() {
        System.out.println("تشغيل اختبار درجة النجاح غير الصحيحة...");
        
        // ترتيب البيانات (Arrange)
        List<Double> grades = Arrays.asList(85.0, 75.0, 65.0);
        double invalidPassingGrade = -10.0; // درجة نجاح غير صحيحة
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(grades, invalidPassingGrade);
        
        // التحقق من النتائج (Assert)
        assertTrue(result.getClassPerformance().contains("خطأ"));
        
        System.out.println("✓ اختبار درجة النجاح غير الصحيحة نجح");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 6: اختبار درجات تحتوي على قيم null
     * Test 6: Test grades with null values
     * يختبر فرع التحقق من صحة الدرجات الفردية
     */
    @Test
    @DisplayName("اختبار الدرجات مع قيم null - Test Grades with Null Values")
    void testGradesWithNullValues() {
        System.out.println("تشغيل اختبار الدرجات مع قيم null...");
        
        // ترتيب البيانات (Arrange)
        List<Double> gradesWithNull = Arrays.asList(85.0, null, 75.0, 65.0, null);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(gradesWithNull, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals(5, result.getTotalStudents()); // يجب أن يحسب جميع العناصر
        assertEquals(3, result.getPassedStudents()); // فقط الدرجات الصحيحة
        assertEquals(75.0, result.getAverageGrade(), 0.01); // متوسط الدرجات الصحيحة فقط
        
        System.out.println("✓ اختبار الدرجات مع قيم null نجح");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 7: اختبار الأداء المتوسط
     * Test 7: Test average performance
     * يختبر فرع الأداء المتوسط للفصل
     */
    @Test
    @DisplayName("اختبار الأداء المتوسط - Test Average Performance")
    void testAveragePerformance() {
        System.out.println("تشغيل اختبار الأداء المتوسط...");
        
        // ترتيب البيانات (Arrange)
        List<Double> averageGrades = Arrays.asList(70.0, 68.0, 72.0, 65.0, 75.0);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(averageGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals("متوسط", result.getClassPerformance());
        assertEquals(5, result.getTotalStudents());
        assertEquals(5, result.getPassedStudents());
        assertEquals(0, result.getFailedStudents());
        assertEquals(70.0, result.getAverageGrade(), 0.01);
        
        System.out.println("✓ اختبار الأداء المتوسط نجح");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 8: اختبار درجات خارج النطاق
     * Test 8: Test grades out of range
     * يختبر فرع التحقق من الدرجات غير الصحيحة
     */
    @Test
    @DisplayName("اختبار الدرجات خارج النطاق - Test Out of Range Grades")
    void testOutOfRangeGrades() {
        System.out.println("تشغيل اختبار الدرجات خارج النطاق...");
        
        // ترتيب البيانات (Arrange)
        List<Double> invalidGrades = Arrays.asList(85.0, -5.0, 105.0, 75.0, 65.0);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(invalidGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals(5, result.getTotalStudents()); // العدد الكلي
        assertEquals(3, result.getPassedStudents()); // فقط الدرجات الصحيحة والناجحة
        assertEquals(75.0, result.getAverageGrade(), 0.01); // متوسط الدرجات الصحيحة
        
        System.out.println("✓ اختبار الدرجات خارج النطاق نجح");
        System.out.println(result.toString());
    }
}
