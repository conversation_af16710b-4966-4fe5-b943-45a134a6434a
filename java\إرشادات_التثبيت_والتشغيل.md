# إرشادات التثبيت والتشغيل
# Installation and Running Instructions

## المتطلبات الأساسية (Prerequisites)

### 1. Java Development Kit (JDK)
- **الإصدار المطلوب:** Java 11 أو أحدث
- **التحميل من:** [Oracle JDK](https://www.oracle.com/java/technologies/downloads/) أو [OpenJDK](https://openjdk.org/)

### 2. Apache Maven
- **الإصدار المطلوب:** Maven 3.6 أو أحدث  
- **التحميل من:** [Apache Maven](https://maven.apache.org/download.cgi)

## خطوات التثبيت (Installation Steps)

### الخطوة 1: تثبيت Java
1. حمل JDK من الرابط أعلاه
2. ثب<PERSON> JDK على النظام
3. تأكد من إعداد متغير البيئة `JAVA_HOME`
4. أض<PERSON> `%JAVA_HOME%\bin` إلى متغير `PATH`

**للتحقق من التثبيت:**
```bash
java -version
javac -version
```

### الخطوة 2: تثبيت Maven
1. حمل Maven من الرابط أعلاه
2. استخرج الملفات إلى مجلد (مثل `C:\apache-maven-3.9.4`)
3. أضف مجلد `bin` إلى متغير `PATH`
4. أنشئ متغير بيئة `M2_HOME` يشير إلى مجلد Maven

**للتحقق من التثبيت:**
```bash
mvn -version
```

## طرق تشغيل المشروع

### الطريقة الأولى: تشغيل بسيط بدون Maven

إذا لم يكن Maven مثبتاً، يمكنك تشغيل المشروع بشكل بسيط:

```bash
# الانتقال إلى مجلد المشروع
cd java

# تجميع الكود
javac -d . src/main/java/com/testing/*.java

# تشغيل المثال
java com.testing.TestRunner
```

أو استخدم الملف المساعد:
```bash
compile-and-run.bat
```

### الطريقة الثانية: تشغيل كامل باستخدام Maven

#### 1. تشغيل الاختبارات الأساسية
```bash
cd java
mvn clean compile test
```

#### 2. إنشاء تقرير تغطية الكود
```bash
mvn clean test jacoco:report
```
**مكان التقرير:** `target/site/jacoco/index.html`

#### 3. تشغيل اختبار التحور (Mutation Testing)
```bash
mvn clean test org.pitest:pitest-maven:mutationCoverage
```
**مكان التقرير:** `target/pit-reports/[timestamp]/index.html`

#### 4. تشغيل جميع الاختبارات والتقارير
```bash
mvn clean compile test jacoco:report org.pitest:pitest-maven:mutationCoverage
```

## عرض التقارير

### تقرير تغطية الكود (JaCoCo)
1. افتح الملف: `target/site/jacoco/index.html`
2. ستجد:
   - نسبة تغطية الخطوط
   - نسبة تغطية الفروع  
   - نسبة تغطية الطرق
   - تفاصيل لكل كلاس

### تقرير اختبار التحور (PIT)
1. افتح الملف: `target/pit-reports/[timestamp]/index.html`
2. ستجد:
   - نسبة قتل المتحورات
   - تفاصيل المتحورات الناجية
   - أنواع التحورات المختبرة

## حل المشاكل الشائعة

### مشكلة: "mvn command not found"
**الحل:**
- تأكد من تثبيت Maven بشكل صحيح
- تأكد من إضافة Maven إلى متغير PATH
- أعد تشغيل Command Prompt

### مشكلة: "JAVA_HOME not set"
**الحل:**
```bash
# Windows
set JAVA_HOME=C:\Program Files\Java\jdk-11.0.x
set PATH=%JAVA_HOME%\bin;%PATH%

# أو أضف هذه المتغيرات في إعدادات النظام
```

### مشكلة: "Package does not exist"
**الحل:**
- تأكد من أن هيكل المجلدات صحيح
- تأكد من أن ملفات `.java` في المكان الصحيح

### مشكلة: مشاكل في الترميز (Encoding)
**الحل:**
```bash
# أضف هذا الخيار عند التجميع
javac -encoding UTF-8 -d . src/main/java/com/testing/*.java
```

## ملفات المشروع المهمة

```
java/
├── pom.xml                           # إعدادات Maven
├── src/main/java/com/testing/
│   ├── StudentGradeCalculator.java   # الكود الرئيسي
│   └── TestRunner.java               # مثال للتشغيل
├── src/test/java/com/testing/
│   └── StudentGradeCalculatorTest.java # اختبارات JUnit
├── compile-and-run.bat               # تشغيل بسيط
├── README.md                         # دليل المشروع
└── تقرير_المشروع.md                  # التقرير النهائي
```

## نصائح مهمة

1. **تأكد من إصدار Java:** استخدم Java 11 أو أحدث
2. **تشغيل الاختبارات أولاً:** تأكد من نجاح جميع الاختبارات قبل إنشاء التقارير
3. **حفظ التقارير:** احفظ تقارير HTML قبل تشغيل Maven مرة أخرى
4. **مراجعة النتائج:** تأكد من أن تغطية الكود 100% ونسبة قتل المتحورات عالية

## للمساعدة

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Java و Maven بشكل صحيح
2. تحقق من متغيرات البيئة
3. جرب التشغيل البسيط أولاً باستخدام `compile-and-run.bat`
4. راجع رسائل الخطأ بعناية

---

**ملاحظة:** هذا المشروع مصمم ليعمل على Windows، macOS، و Linux مع تعديلات بسيطة في أوامر Terminal.
