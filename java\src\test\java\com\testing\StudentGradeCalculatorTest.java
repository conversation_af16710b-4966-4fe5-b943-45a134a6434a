package com.testing;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

/**
 * فئة اختبار لفئة حساب درجات الطلاب
 * Test class for StudentGradeCalculator
 */
public class StudentGradeCalculatorTest {
    
    private StudentGradeCalculator calculator;
    
    @BeforeEach
    void setUp() {
        calculator = new StudentGradeCalculator();
    }
    
    /**
     * اختبار 1: اختبار حالة الأداء الممتاز
     * Test 1: Test excellent performance case
     */
    @Test
    @DisplayName("اختبار الأداء الممتاز - Test Excellent Performance")
    void testExcellentPerformance() {
        // ترتيب البيانات (Arrange)
        List<Double> excellentGrades = Arrays.asList(95.0, 92.0, 88.0, 90.0, 85.0);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(excellentGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals("ممتاز", result.getClassPerformance());
        assertEquals(5, result.getTotalStudents());
        assertEquals(5, result.getPassedStudents());
        assertEquals(0, result.getFailedStudents());
        assertEquals(90.0, result.getAverageGrade(), 0.01);
        assertEquals(3, result.getExcellentStudents()); // 95, 92, 90
        
        System.out.println("اختبار الأداء الممتاز نجح:");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 2: اختبار حالة الأداء المختلط (ناجح وراسب)
     * Test 2: Test mixed performance case (pass and fail)
     */
    @Test
    @DisplayName("اختبار الأداء المختلط - Test Mixed Performance")
    void testMixedPerformance() {
        // ترتيب البيانات (Arrange)
        List<Double> mixedGrades = Arrays.asList(85.0, 45.0, 78.0, 55.0, 92.0, 30.0, 88.0);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(mixedGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals("جيد", result.getClassPerformance());
        assertEquals(7, result.getTotalStudents());
        assertEquals(4, result.getPassedStudents()); // 85, 78, 92, 88
        assertEquals(3, result.getFailedStudents());  // 45, 55, 30
        assertEquals(67.57, result.getAverageGrade(), 0.01);
        assertEquals(1, result.getExcellentStudents()); // 92
        
        System.out.println("اختبار الأداء المختلط نجح:");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 3: اختبار حالة الأداء الضعيف
     * Test 3: Test poor performance case
     */
    @Test
    @DisplayName("اختبار الأداء الضعيف - Test Poor Performance")
    void testPoorPerformance() {
        // ترتيب البيانات (Arrange)
        List<Double> poorGrades = Arrays.asList(45.0, 35.0, 50.0, 40.0, 55.0);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(poorGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals("ضعيف", result.getClassPerformance());
        assertEquals(5, result.getTotalStudents());
        assertEquals(0, result.getPassedStudents());
        assertEquals(5, result.getFailedStudents());
        assertEquals(45.0, result.getAverageGrade(), 0.01);
        assertEquals(0, result.getExcellentStudents());
        
        System.out.println("اختبار الأداء الضعيف نجح:");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 4: اختبار حالة القائمة الفارغة
     * Test 4: Test empty list case
     */
    @Test
    @DisplayName("اختبار القائمة الفارغة - Test Empty List")
    void testEmptyGradesList() {
        // ترتيب البيانات (Arrange)
        List<Double> emptyGrades = new ArrayList<>();
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(emptyGrades, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertTrue(result.getClassPerformance().contains("خطأ"));
        assertEquals(0, result.getTotalStudents());
        
        System.out.println("اختبار القائمة الفارغة نجح:");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 5: اختبار درجة نجاح غير صحيحة
     * Test 5: Test invalid passing grade
     */
    @Test
    @DisplayName("اختبار درجة النجاح غير الصحيحة - Test Invalid Passing Grade")
    void testInvalidPassingGrade() {
        // ترتيب البيانات (Arrange)
        List<Double> grades = Arrays.asList(85.0, 75.0, 65.0);
        double invalidPassingGrade = -10.0; // درجة نجاح غير صحيحة
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(grades, invalidPassingGrade);
        
        // التحقق من النتائج (Assert)
        assertTrue(result.getClassPerformance().contains("خطأ"));
        
        System.out.println("اختبار درجة النجاح غير الصحيحة نجح:");
        System.out.println(result.toString());
    }
    
    /**
     * اختبار 6: اختبار درجات تحتوي على قيم null
     * Test 6: Test grades with null values
     */
    @Test
    @DisplayName("اختبار الدرجات مع قيم null - Test Grades with Null Values")
    void testGradesWithNullValues() {
        // ترتيب البيانات (Arrange)
        List<Double> gradesWithNull = Arrays.asList(85.0, null, 75.0, 65.0, null);
        double passingGrade = 60.0;
        
        // تنفيذ الاختبار (Act)
        StudentGradeCalculator.GradeAnalysisResult result = 
            calculator.analyzeStudentGrades(gradesWithNull, passingGrade);
        
        // التحقق من النتائج (Assert)
        assertEquals(5, result.getTotalStudents()); // يجب أن يحسب جميع العناصر
        assertEquals(3, result.getPassedStudents()); // فقط الدرجات الصحيحة
        assertEquals(75.0, result.getAverageGrade(), 0.01); // متوسط الدرجات الصحيحة فقط
        
        System.out.println("اختبار الدرجات مع قيم null نجح:");
        System.out.println(result.toString());
    }
}
